/**
 * @description This component renders a dedicated section for Klusgebied+ investment packages. It showcases three distinct tiers (Starter, Groei, Elite) for craftsmen to invest in the platform for benefits like higher visibility and guaranteed jobs. The component is designed to be fully responsive, featuring a side-by-side card layout on desktop and a horizontal scroll on mobile. It includes a functional modal form to capture investment interest, which saves data to Supabase and sends an email notification. Key variables include the investmentPackages data array, form state, and submission status management for the modal.
 */

import React, { useState } from "react";
import {
  CheckCircle,
  ArrowRight,
  X,
  Loader,
  AlertTriangle,
  User,
  Mail,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const InvestmentSection = () => {
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(""); // 'success', 'error', 'duplicate'

  const investmentPackages = [
    {
      id: "starter",
      name: "Starter",
      price: 1000,
      color: "yellow",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      buttonColor: "bg-yellow-500 hover:bg-yellow-600",
      features: [
        "3 maanden gegarandeerde plaatsing bovenaan bij relevante klussen",
        "Prioriteit bij reactietoegang",
        "Soldo-tegoed van €200",
      ],
    },
    {
      id: "groei",
      name: "Groei",
      price: 2500,
      color: "blue",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      buttonColor: "bg-blue-500 hover:bg-blue-600",
      popular: true,
      features: [
        "6 maanden gegarandeerde klussen (min. 10)",
        'Badge "Klusgebied+ Partner" in profiel',
        "Persoonlijke accountmanager",
        "Soldo-tegoed van €500",
      ],
    },
    {
      id: "elite",
      name: "Elite",
      price: 5000,
      color: "purple",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      buttonColor: "bg-purple-500 hover:bg-purple-600",
      features: [
        "12 maanden gegarandeerd werk (min. 25 klussen)",
        "Mogelijkheid tot exclusieve samenwerkingen met woningcorporaties",
        "Uitnodiging voor partnernetwerk-events",
        "Soldo-tegoed van €1000",
      ],
    },
  ];

  const handlePackageSelect = (pkg) => {
    setSelectedPackage(pkg);
    setShowModal(true);
    setSubmitStatus("");
    setFormData({ name: "", email: "" });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    // e.preventDefault();
    // if (!selectedPackage || !formData.name || !formData.email) return;
    // setIsSubmitting(true);
    // setSubmitStatus("");
    // try {
    //   // 1. Check for duplicate submissions
    //   const { data: existingSubmissions } = await supabase
    //     .from("6865560a10605c25f99173ec_investment_submissions")
    //     .select("*")
    //     .eq("email", formData.email)
    //     .eq("package_name", selectedPackage.name)
    //     .eq("email_sent", true);
    //   if (existingSubmissions && existingSubmissions.length > 0) {
    //     setSubmitStatus("duplicate");
    //     setIsSubmitting(false);
    //     return;
    //   }
    //   // 2. Send email notification
    //   const emailSubject = `[Klusgebied+] Nieuwe Investeringsinteresse: ${selectedPackage.name}`;
    //   const emailHtml = `
    //     <div style="font-family: Arial, sans-serif; max-width:600px; margin:auto; border:1px solid #e0e0e0; border-radius:8px; overflow:hidden;">
    //       <div style="background:#00B894; color:#fff; padding:16px; text-align:center;">
    //         <img src="https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png" alt="Klusgebied Logo" style="height:40px; margin-bottom:8px;">
    //         <h2 style="margin:0;">Klusgebied+</h2>
    //       </div>
    //       <div style="padding:24px; background:#fafafa;">
    //         <h3 style="color:#00B894; margin-top:0;">Nieuwe Investeringsinteresse</h3>
    //         <table style="width:100%; border-collapse:collapse;">
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Naam:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">${
    //             formData.name
    //           }</td></tr>
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Email:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">${
    //             formData.email
    //           }</td></tr>
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Pakket:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">${
    //             selectedPackage.name
    //           }</td></tr>
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Investering:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">€${selectedPackage.price.toLocaleString()}</td></tr>
    //         </table>
    //       </div>
    //       <div style="background:#f0f0f0; color:#555; padding:12px; font-size:12px; text-align:center;">
    //         Verstuurd op ${new Date().toLocaleString()}<br>
    //         Powered by Klusgebied
    //       </div>
    //     </div>
    //   `;
    //   let emailSent = false;
    //   let emailSentAt = null;
    //   try {
    //     const response = await fetch("https://api.heybossai.com/v1/run", {
    //       method: "POST",
    //       headers: {
    //         "Content-Type": "application/json",
    //         Authorization: `Bearer ${import.meta.env.VITE_API_KEY}`,
    //       },
    //       body: JSON.stringify({
    //         model: "aws/send-email",
    //         inputs: {
    //           receivers: config.user_email,
    //           title: emailSubject,
    //           body_html: emailHtml,
    //           project_id: config.project_id,
    //         },
    //       }),
    //     });
    //     const result = await response.json();
    //     if (result.send_email_status === "success") {
    //       emailSent = true;
    //       emailSentAt = new Date().toISOString();
    //     }
    //   } catch (emailError) {
    //     console.error("Email sending failed:", emailError);
    //   }
    //   // 3. Save to database
    //   const { error: dbError } = await supabase
    //     .from("6865560a10605c25f99173ec_investment_submissions")
    //     .insert({
    //       name: formData.name,
    //       email: formData.email,
    //       package_name: selectedPackage.name,
    //       investment_amount: selectedPackage.price,
    //       email_sent: emailSent,
    //       email_sent_at: emailSentAt,
    //     });
    //   if (dbError) {
    //     console.error("Database error:", dbError);
    //     setSubmitStatus("error");
    //   } else {
    //     setSubmitStatus("success");
    //   }
    // } catch (error) {
    //   console.error("Submission error:", error);
    //   setSubmitStatus("error");
    // } finally {
    //   setIsSubmitting(false);
    // }
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedPackage(null);
    setFormData({ name: "", email: "" });
    setSubmitStatus("");
  };

  return (
    <>
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-teal-100 rounded-full mb-6">
              <span className="text-2xl">📈</span>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Investeer in Klusgebied+
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Word premium partner van Klusgebied via Klusgebied+. Investeer in
              jouw toekomst als vakman. Kies jouw pakket en profiteer van
              gegarandeerde klussen, hogere zichtbaarheid en vaste klanten via
              ons platform.
            </p>
          </div>

          {/* Investment Packages - Desktop: Side by side, Mobile: Horizontal scroll */}
          <div className="lg:grid lg:grid-cols-3 lg:gap-8 lg:max-w-7xl lg:mx-auto">
            <div className="flex lg:contents gap-6 overflow-x-auto lg:overflow-visible pb-6 lg:pb-0 px-4 lg:px-0">
              {investmentPackages.map((pkg, index) => (
                <div
                  key={pkg.id}
                  className={`
                    relative flex-shrink-0 w-80 lg:w-full ${pkg.bgColor} ${
                    pkg.borderColor
                  } 
                    border-2 rounded-xl p-8 motion-preset-slide-up motion-delay-${
                      index * 100
                    }
                    ${pkg.popular ? "ring-4 ring-teal-200 scale-105" : ""}
                  `}
                >
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                        Meest Gekozen
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {pkg.name}
                    </h3>
                    <div className="text-4xl font-bold text-gray-900 mb-1">
                      €{pkg.price.toLocaleString()}
                    </div>
                    <p className="text-gray-600">Eenmalige investering</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <CheckCircle className="w-5 h-5 text-teal-500 mt-0.5 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => handlePackageSelect(pkg)}
                    className={`
                      w-full ${pkg.buttonColor} text-white font-semibold py-4 px-6 rounded-lg 
                      transition-all duration-200 flex items-center justify-center group
                    `}
                  >
                    Investeer Nu
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className="text-gray-600 mb-6">
              Vragen over Klusgebied+ investeringsmogelijkheden?
            </p>
            <button
              onClick={() => navigate("/contact")}
              className="bg-gray-800 hover:bg-gray-900 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
            >
              Neem Contact Op
            </button>
          </div>
        </div>
      </section>

      {/* Investment Interest Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl p-8 max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Interesse in {selectedPackage?.name}
              </h3>
              <button
                onClick={closeModal}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={isSubmitting}
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {submitStatus === "success" && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                  <div>
                    <p className="font-semibold text-green-800">
                      Interesse geregistreerd!
                    </p>
                    <p className="text-green-700 text-sm">
                      We nemen binnenkort contact met je op.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {submitStatus === "duplicate" && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3" />
                  <div>
                    <p className="font-semibold text-yellow-800">
                      Al geregistreerd
                    </p>
                    <p className="text-yellow-700 text-sm">
                      Je hebt al interesse getoond voor dit pakket.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {submitStatus === "error" && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-500 mr-3" />
                  <div>
                    <p className="font-semibold text-red-800">
                      Er ging iets mis
                    </p>
                    <p className="text-red-700 text-sm">
                      Probeer het later opnieuw of neem contact op.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {submitStatus !== "success" && (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Volledige Naam *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:opacity-50"
                      placeholder="Jouw naam"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Adres *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:opacity-50"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {selectedPackage?.name} Pakket - €
                    {selectedPackage?.price.toLocaleString()}
                  </h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {selectedPackage?.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <CheckCircle className="w-4 h-4 text-teal-500 mt-0.5 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={closeModal}
                    disabled={isSubmitting}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    Annuleren
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !formData.name || !formData.email}
                    className="flex-1 bg-teal-500 hover:bg-teal-600 text-white font-semibold px-6 py-3 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader className="w-5 h-5 mr-2 animate-spin" />
                        Versturen...
                      </>
                    ) : (
                      "Interesse Tonen"
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default InvestmentSection;
